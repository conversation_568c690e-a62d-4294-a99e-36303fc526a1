package com.focusflow.data.repository

import com.focusflow.data.dao.WishlistItemDao
import com.focusflow.data.model.WishlistItem
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WishlistRepository @Inject constructor(
    private val wishlistItemDao: WishlistItemDao
) {
    
    fun getAllActiveWishlistItems(): Flow<List<WishlistItem>> = 
        wishlistItemDao.getAllActiveWishlistItems()
    
    fun getPurchasedWishlistItems(): Flow<List<WishlistItem>> = 
        wishlistItemDao.getPurchasedWishlistItems()
    
    suspend fun getItemsWithExpiredDelay(currentTime: LocalDateTime): List<WishlistItem> = 
        wishlistItemDao.getItemsWithExpiredDelay(currentTime)
    
    fun getActiveDelayItems(): Flow<List<WishlistItem>> = 
        wishlistItemDao.getActiveDelayItems()
    
    fun getWishlistItemsByCategory(category: String): Flow<List<WishlistItem>> = 
        wishlistItemDao.getWishlistItemsByCategory(category)
    
    fun getWishlistItemsByPriority(priority: String): Flow<List<WishlistItem>> = 
        wishlistItemDao.getWishlistItemsByPriority(priority)
    
    fun getWishlistItemsByPriceRange(minPrice: Double, maxPrice: Double): Flow<List<WishlistItem>> = 
        wishlistItemDao.getWishlistItemsByPriceRange(minPrice, maxPrice)
    
    suspend fun getWishlistItemById(id: Long): WishlistItem? = 
        wishlistItemDao.getWishlistItemById(id)
    
    suspend fun getActiveDelayCount(): Int = 
        wishlistItemDao.getActiveDelayCount()
    
    suspend fun getAverageWishlistPrice(): Double? = 
        wishlistItemDao.getAverageWishlistPrice()
    
    suspend fun getTotalWishlistValue(): Double? = 
        wishlistItemDao.getTotalWishlistValue()
    
    suspend fun insertWishlistItem(wishlistItem: WishlistItem): Long = 
        wishlistItemDao.insertWishlistItem(wishlistItem)
    
    suspend fun updateWishlistItem(wishlistItem: WishlistItem) = 
        wishlistItemDao.updateWishlistItem(wishlistItem)
    
    suspend fun deleteWishlistItem(wishlistItem: WishlistItem) = 
        wishlistItemDao.deleteWishlistItem(wishlistItem)
    
    suspend fun markAsPurchased(id: Long, purchaseDate: LocalDateTime, actualPrice: Double) = 
        wishlistItemDao.markAsPurchased(id, purchaseDate, actualPrice)
    
    suspend fun removeDelay(id: Long) = 
        wishlistItemDao.removeDelay(id)
    
    suspend fun updateReflection(id: Long, stillWanted: Boolean, notes: String?) = 
        wishlistItemDao.updateReflection(id, stillWanted, notes)
    
    suspend fun deletePurchasedItemsOlderThan(cutoffDate: LocalDateTime) = 
        wishlistItemDao.deletePurchasedItemsOlderThan(cutoffDate)
    
    // Helper methods for business logic
    suspend fun addItemToWishlist(
        itemName: String,
        estimatedPrice: Double,
        category: String,
        description: String? = null,
        merchant: String? = null,
        delayPeriodHours: Int = 24,
        priority: String = "medium"
    ): Long {
        val now = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault())
        val delayEndTime = now.toInstant(kotlinx.datetime.TimeZone.currentSystemDefault())
            .plus(kotlinx.datetime.DateTimePeriod(hours = delayPeriodHours), kotlinx.datetime.TimeZone.currentSystemDefault())
            .toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault())
        
        val wishlistItem = WishlistItem(
            itemName = itemName,
            estimatedPrice = estimatedPrice,
            category = category,
            description = description,
            merchant = merchant,
            addedDate = now,
            delayPeriodHours = delayPeriodHours,
            delayEndTime = delayEndTime,
            priority = priority
        )
        
        return insertWishlistItem(wishlistItem)
    }
    
    suspend fun extendDelay(id: Long, additionalHours: Int) {
        val item = getWishlistItemById(id)
        item?.let {
            val newDelayEndTime = it.delayEndTime.toInstant(kotlinx.datetime.TimeZone.currentSystemDefault())
                .plus(kotlinx.datetime.DateTimePeriod(hours = additionalHours), kotlinx.datetime.TimeZone.currentSystemDefault())
                .toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault())
            updateWishlistItem(it.copy(delayEndTime = newDelayEndTime))
        }
    }
}
