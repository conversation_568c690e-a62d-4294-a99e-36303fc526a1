Dapp/src/main/java/com/focusflow/data/repository/ExpenseRepository.ktAapp/src/main/java/com/focusflow/data/dao/SpendingReflectionDao.kt@app/src/main/java/com/focusflow/data/model/SpendingReflection.kt:app/src/main/java/com/focusflow/ui/screens/OtherScreens.kt<app/src/main/java/com/focusflow/ui/screens/SettingsScreen.ktBapp/src/main/java/com/focusflow/ui/viewmodel/DashboardViewModel.kt5app/src/main/java/com/focusflow/data/model/Expense.kt>app/src/main/java/com/focusflow/service/GamificationService.kt>app/src/main/java/com/focusflow/data/dao/UserPreferencesDao.ktBapp/src/main/java/com/focusflow/data/model/BudgetRecommendation.kt9app/src/main/java/com/focusflow/data/model/Achievement.ktAapp/src/main/java/com/focusflow/ui/screens/PayoffPlannerScreen.ktLapp/src/main/java/com/focusflow/data/repository/UserPreferencesRepository.ktGapp/src/main/java/com/focusflow/data/repository/CreditCardRepository.kt3app/src/main/java/com/focusflow/data/dao/TaskDao.kt0app/src/main/java/com/focusflow/ui/theme/Type.ktCapp/src/main/java/com/focusflow/ui/viewmodel/OnboardingViewModel.ktGapp/src/main/java/com/focusflow/ui/viewmodel/ImpulseControlViewModel.kt?app/src/main/java/com/focusflow/ui/viewmodel/BudgetViewModel.kt>app/src/main/java/com/focusflow/data/dao/BudgetAnalyticsDao.kt@app/src/main/java/com/focusflow/ui/screens/PlaceholderScreens.kt9app/src/main/java/com/focusflow/receiver/AlarmReceiver.kt>app/src/main/java/com/focusflow/service/NotificationManager.ktIapp/src/main/java/com/focusflow/data/repository/NotificationRepository.kt@app/src/main/java/com/focusflow/ui/viewmodel/AICoachViewModel.kt4app/src/main/java/com/focusflow/di/DatabaseModule.ktCapp/src/main/java/com/focusflow/data/dao/BudgetRecommendationDao.ktAapp/src/main/java/com/focusflow/ui/viewmodel/SettingsViewModel.kt6app/src/main/java/com/focusflow/data/model/HabitLog.kt/app/src/main/java/com/focusflow/MainActivity.ktAapp/src/main/java/com/focusflow/ui/onboarding/OnboardingScreen.kt7app/src/main/java/com/focusflow/data/dao/HabitLogDao.kt;app/src/main/java/com/focusflow/data/database/Converters.kt>app/src/main/java/com/focusflow/service/NotificationService.ktEapp/src/main/java/com/focusflow/data/repository/WishlistRepository.kt7app/src/main/java/com/focusflow/FocusFlowApplication.kt<app/src/main/java/com/focusflow/data/dao/AIInteractionDao.kt;app/src/main/java/com/focusflow/data/model/AIInteraction.kt6app/src/main/java/com/focusflow/data/dao/ExpenseDao.kt=app/src/main/java/com/focusflow/ui/viewmodel/MainViewModel.kt8app/src/main/java/com/focusflow/navigation/Navigation.kt2app/src/main/java/com/focusflow/data/model/Task.kt8app/src/main/java/com/focusflow/data/model/CreditCard.kt@app/src/main/java/com/focusflow/ui/viewmodel/ExpenseViewModel.ktGapp/src/main/java/com/focusflow/ui/viewmodel/EnhancedBudgetViewModel.kt;app/src/main/java/com/focusflow/data/dao/GamificationDao.ktLapp/src/main/java/com/focusflow/data/repository/BudgetAnalyticsRepository.kt:app/src/main/java/com/focusflow/data/model/WishlistItem.kt=app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.kt<app/src/main/java/com/focusflow/data/model/BudgetCategory.kt=app/src/main/java/com/focusflow/data/dao/BudgetCategoryDao.kt9app/src/main/java/com/focusflow/data/dao/CreditCardDao.kt1app/src/main/java/com/focusflow/ui/theme/Shape.ktFapp/src/main/java/com/focusflow/service/BudgetRecommendationService.kt=app/src/main/java/com/focusflow/data/model/UserPreferences.ktIapp/src/main/java/com/focusflow/ui/components/ImpulseControlComponents.ktBapp/src/main/java/com/focusflow/data/database/FocusFlowDatabase.kt1app/src/main/java/com/focusflow/ui/theme/Color.ktKapp/src/main/java/com/focusflow/data/repository/BudgetCategoryRepository.ktOapp/src/main/java/com/focusflow/data/repository/SpendingReflectionRepository.ktDapp/src/main/java/com/focusflow/ui/components/BottomNavigationBar.kt6app/src/main/java/com/focusflow/utils/ErrorHandling.kt1app/src/main/java/com/focusflow/ui/theme/Theme.kt@app/src/main/java/com/focusflow/ui/components/SettingsDialogs.kt8app/src/main/java/com/focusflow/ui/screens/DebtScreen.kt4app/src/main/java/com/focusflow/data/model/Income.kt=app/src/main/java/com/focusflow/ui/screens/DashboardScreen.kt?app/src/main/java/com/focusflow/service/PurchaseDelayService.ktQapp/src/main/java/com/focusflow/data/repository/BudgetRecommendationRepository.kt;app/src/main/java/com/focusflow/data/dao/WishlistItemDao.ktIapp/src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.ktCapp/src/main/java/com/focusflow/ui/onboarding/OnboardingActivity.kt=app/src/main/java/com/focusflow/data/model/BudgetAnalytics.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 