{"logs": [{"outputFile": "com.focusflow.app-mergeReleaseResources-65:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f170a70d96fcd26f910cf5fef71c6ab\\transformed\\jetified-ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "36,37,39,41,42,53,54,55,56,57,58,59,60,62,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3478,3568,3751,3959,4057,5379,5468,5558,5644,5727,5792,5858,5938,6104,6279,6357,6423", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "3563,3640,3855,4052,4141,5463,5553,5639,5722,5787,5853,5933,6017,6173,6352,6418,6539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0e1dc919567705f737931e90f7aead7b\\transformed\\biometric-1.1.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,260,370,504,627,764,883,1010,1105,1243,1374", "endColumns": "105,98,109,133,122,136,118,126,94,137,130,118", "endOffsets": "156,255,365,499,622,759,878,1005,1100,1238,1369,1488"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3645,3860,4146,4256,4390,4513,4650,4769,4896,4991,5129,5260", "endColumns": "105,98,109,133,122,136,118,126,94,137,130,118", "endOffsets": "3746,3954,4251,4385,4508,4645,4764,4891,4986,5124,5255,5374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\904fd36595e0135e8fdca5c15906c24d\\transformed\\core-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2772,2868,2971,3070,3168,3269,3367,6178", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "2863,2966,3065,3163,3264,3362,3473,6274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b7575953e232ac886e0021593ed04f20\\transformed\\appcompat-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,6022", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,6099"}}]}]}