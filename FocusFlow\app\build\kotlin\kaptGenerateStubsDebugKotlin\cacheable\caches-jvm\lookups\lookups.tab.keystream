  Manifest android  AlarmManager android.app  Application android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  Bundle android.app.Activity  
Composable android.app.Activity  Context android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  String android.app.Service  BroadcastReceiver android.content  Context android.content  Intent android.content  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  NotificationCompat !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  
ALARM_SERVICE android.content.Context  Bundle android.content.Context  
Composable android.content.Context  Context android.content.Context  IBinder android.content.Context  Int android.content.Context  Intent android.content.Context  String android.content.Context  getSystemService android.content.Context  Bundle android.content.ContextWrapper  
Composable android.content.ContextWrapper  Context android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  String android.content.ContextWrapper  PackageManager android.content.pm  Build 
android.os  Bundle 
android.os  IBinder 
android.os  Log android.util  Bundle  android.view.ContextThemeWrapper  
Composable  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  
Composable #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultContracts !androidx.activity.result.contract  
Composable androidx.compose.animation.core  Image androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Pair "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	GridCells %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  
selectable %androidx.compose.foundation.selection  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Colors androidx.compose.material  ColumnScope androidx.compose.material  
Composable androidx.compose.material  
MaterialTheme androidx.compose.material  Pair androidx.compose.material  Shapes androidx.compose.material  SnackbarDuration androidx.compose.material  SnackbarHostState androidx.compose.material  
Typography androidx.compose.material  com androidx.compose.material  
darkColors androidx.compose.material  lightColors androidx.compose.material  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  CheckCircle ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  List ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  ShoppingCart ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  ColumnScope &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  Pair &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  Screen &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  ShoppingCart &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  ColumnScope androidx.compose.runtime  
Composable androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  Pair androidx.compose.runtime  com androidx.compose.runtime  getValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  Bundle #androidx.core.app.ComponentActivity  
Composable #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  from +androidx.core.app.NotificationManagerCompat  
ContextCompat androidx.core.content  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  AICoachUiState androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  BudgetCategory androidx.lifecycle.ViewModel  BudgetCategoryRepository androidx.lifecycle.ViewModel  
BudgetUiState androidx.lifecycle.ViewModel  
CreditCard androidx.lifecycle.ViewModel  CreditCardRepository androidx.lifecycle.ViewModel  DashboardUiState androidx.lifecycle.ViewModel  DebtUiState androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  Expense androidx.lifecycle.ViewModel  ExpenseRepository androidx.lifecycle.ViewModel  ExpenseUiState androidx.lifecycle.ViewModel  Flow androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  	LocalDate androidx.lifecycle.ViewModel  
LocalDateTime androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MainUiState androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  NotificationRepository androidx.lifecycle.ViewModel  OnboardingUiState androidx.lifecycle.ViewModel  Pair androidx.lifecycle.ViewModel  PayoffCalculation androidx.lifecycle.ViewModel  
PayoffPlan androidx.lifecycle.ViewModel  PayoffStrategy androidx.lifecycle.ViewModel  SettingsUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Triple androidx.lifecycle.ViewModel  UserPreferencesRepository androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  collectAsStateWithLifecycle androidx.lifecycle.compose  
NavController androidx.navigation  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  kotlinx 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  AIInteractionDao androidx.room.RoomDatabase  AchievementDao androidx.room.RoomDatabase  BudgetCategoryDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  
CreditCardDao androidx.room.RoomDatabase  
ExpenseDao androidx.room.RoomDatabase  FocusFlowDatabase androidx.room.RoomDatabase  HabitLogDao androidx.room.RoomDatabase  TaskDao androidx.room.RoomDatabase  UserPreferencesDao androidx.room.RoomDatabase  UserStatsDao androidx.room.RoomDatabase  
VirtualPetDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  
Composable 
com.focusflow  FocusFlowApp 
com.focusflow  FocusFlowApplication 
com.focusflow  MainActivity 
com.focusflow  MainAppContent 
com.focusflow  R 
com.focusflow  Bundle com.focusflow.MainActivity  
AIInteraction com.focusflow.data.dao  AIInteractionDao com.focusflow.data.dao  Achievement com.focusflow.data.dao  AchievementDao com.focusflow.data.dao  Boolean com.focusflow.data.dao  BudgetCategory com.focusflow.data.dao  BudgetCategoryDao com.focusflow.data.dao  
Converters com.focusflow.data.dao  
CreditCard com.focusflow.data.dao  
CreditCardDao com.focusflow.data.dao  Dao com.focusflow.data.dao  Delete com.focusflow.data.dao  Double com.focusflow.data.dao  Expense com.focusflow.data.dao  
ExpenseDao com.focusflow.data.dao  HabitLog com.focusflow.data.dao  HabitLogDao com.focusflow.data.dao  Insert com.focusflow.data.dao  Int com.focusflow.data.dao  List com.focusflow.data.dao  Long com.focusflow.data.dao  OnConflictStrategy com.focusflow.data.dao  Query com.focusflow.data.dao  SingletonComponent com.focusflow.data.dao  String com.focusflow.data.dao  Task com.focusflow.data.dao  TaskDao com.focusflow.data.dao  Update com.focusflow.data.dao  UserPreferences com.focusflow.data.dao  UserPreferencesDao com.focusflow.data.dao  	UserStats com.focusflow.data.dao  UserStatsDao com.focusflow.data.dao  
VirtualPet com.focusflow.data.dao  
VirtualPetDao com.focusflow.data.dao  Volatile com.focusflow.data.dao  kotlinx com.focusflow.data.dao  
AIInteraction 'com.focusflow.data.dao.AIInteractionDao  Delete 'com.focusflow.data.dao.AIInteractionDao  Flow 'com.focusflow.data.dao.AIInteractionDao  Insert 'com.focusflow.data.dao.AIInteractionDao  Int 'com.focusflow.data.dao.AIInteractionDao  List 'com.focusflow.data.dao.AIInteractionDao  Long 'com.focusflow.data.dao.AIInteractionDao  Query 'com.focusflow.data.dao.AIInteractionDao  String 'com.focusflow.data.dao.AIInteractionDao  kotlinx 'com.focusflow.data.dao.AIInteractionDao  Achievement %com.focusflow.data.dao.AchievementDao  Flow %com.focusflow.data.dao.AchievementDao  Insert %com.focusflow.data.dao.AchievementDao  Int %com.focusflow.data.dao.AchievementDao  List %com.focusflow.data.dao.AchievementDao  Long %com.focusflow.data.dao.AchievementDao  OnConflictStrategy %com.focusflow.data.dao.AchievementDao  Query %com.focusflow.data.dao.AchievementDao  String %com.focusflow.data.dao.AchievementDao  Update %com.focusflow.data.dao.AchievementDao  kotlinx %com.focusflow.data.dao.AchievementDao  BudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  Delete (com.focusflow.data.dao.BudgetCategoryDao  Double (com.focusflow.data.dao.BudgetCategoryDao  Flow (com.focusflow.data.dao.BudgetCategoryDao  Insert (com.focusflow.data.dao.BudgetCategoryDao  Int (com.focusflow.data.dao.BudgetCategoryDao  List (com.focusflow.data.dao.BudgetCategoryDao  Long (com.focusflow.data.dao.BudgetCategoryDao  Query (com.focusflow.data.dao.BudgetCategoryDao  String (com.focusflow.data.dao.BudgetCategoryDao  Update (com.focusflow.data.dao.BudgetCategoryDao  deleteBudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  updateBudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  updateSpentAmount (com.focusflow.data.dao.BudgetCategoryDao  
CreditCard $com.focusflow.data.dao.CreditCardDao  Delete $com.focusflow.data.dao.CreditCardDao  Double $com.focusflow.data.dao.CreditCardDao  Flow $com.focusflow.data.dao.CreditCardDao  Insert $com.focusflow.data.dao.CreditCardDao  List $com.focusflow.data.dao.CreditCardDao  	LocalDate $com.focusflow.data.dao.CreditCardDao  Long $com.focusflow.data.dao.CreditCardDao  Query $com.focusflow.data.dao.CreditCardDao  Update $com.focusflow.data.dao.CreditCardDao  deactivateCreditCard $com.focusflow.data.dao.CreditCardDao  deleteCreditCard $com.focusflow.data.dao.CreditCardDao  updateCreditCard $com.focusflow.data.dao.CreditCardDao  Delete !com.focusflow.data.dao.ExpenseDao  Double !com.focusflow.data.dao.ExpenseDao  Expense !com.focusflow.data.dao.ExpenseDao  Flow !com.focusflow.data.dao.ExpenseDao  Insert !com.focusflow.data.dao.ExpenseDao  List !com.focusflow.data.dao.ExpenseDao  
LocalDateTime !com.focusflow.data.dao.ExpenseDao  Long !com.focusflow.data.dao.ExpenseDao  Query !com.focusflow.data.dao.ExpenseDao  String !com.focusflow.data.dao.ExpenseDao  Update !com.focusflow.data.dao.ExpenseDao  
deleteExpense !com.focusflow.data.dao.ExpenseDao  deleteExpenseById !com.focusflow.data.dao.ExpenseDao  
updateExpense !com.focusflow.data.dao.ExpenseDao  Delete "com.focusflow.data.dao.HabitLogDao  Flow "com.focusflow.data.dao.HabitLogDao  HabitLog "com.focusflow.data.dao.HabitLogDao  Insert "com.focusflow.data.dao.HabitLogDao  Int "com.focusflow.data.dao.HabitLogDao  List "com.focusflow.data.dao.HabitLogDao  	LocalDate "com.focusflow.data.dao.HabitLogDao  Long "com.focusflow.data.dao.HabitLogDao  OnConflictStrategy "com.focusflow.data.dao.HabitLogDao  Query "com.focusflow.data.dao.HabitLogDao  String "com.focusflow.data.dao.HabitLogDao  Update "com.focusflow.data.dao.HabitLogDao  Delete com.focusflow.data.dao.TaskDao  Flow com.focusflow.data.dao.TaskDao  Insert com.focusflow.data.dao.TaskDao  Int com.focusflow.data.dao.TaskDao  List com.focusflow.data.dao.TaskDao  
LocalDateTime com.focusflow.data.dao.TaskDao  Long com.focusflow.data.dao.TaskDao  Query com.focusflow.data.dao.TaskDao  String com.focusflow.data.dao.TaskDao  Task com.focusflow.data.dao.TaskDao  Update com.focusflow.data.dao.TaskDao  Boolean )com.focusflow.data.dao.UserPreferencesDao  Flow )com.focusflow.data.dao.UserPreferencesDao  Insert )com.focusflow.data.dao.UserPreferencesDao  OnConflictStrategy )com.focusflow.data.dao.UserPreferencesDao  Query )com.focusflow.data.dao.UserPreferencesDao  String )com.focusflow.data.dao.UserPreferencesDao  Update )com.focusflow.data.dao.UserPreferencesDao  UserPreferences )com.focusflow.data.dao.UserPreferencesDao  insertUserPreferences )com.focusflow.data.dao.UserPreferencesDao  updateBudgetPeriod )com.focusflow.data.dao.UserPreferencesDao  updateDarkModeEnabled )com.focusflow.data.dao.UserPreferencesDao  updateFontSize )com.focusflow.data.dao.UserPreferencesDao  updateNotificationsEnabled )com.focusflow.data.dao.UserPreferencesDao  updateUserPreferences )com.focusflow.data.dao.UserPreferencesDao  Double #com.focusflow.data.dao.UserStatsDao  Flow #com.focusflow.data.dao.UserStatsDao  Insert #com.focusflow.data.dao.UserStatsDao  Int #com.focusflow.data.dao.UserStatsDao  OnConflictStrategy #com.focusflow.data.dao.UserStatsDao  Query #com.focusflow.data.dao.UserStatsDao  Update #com.focusflow.data.dao.UserStatsDao  	UserStats #com.focusflow.data.dao.UserStatsDao  Flow $com.focusflow.data.dao.VirtualPetDao  Insert $com.focusflow.data.dao.VirtualPetDao  Int $com.focusflow.data.dao.VirtualPetDao  OnConflictStrategy $com.focusflow.data.dao.VirtualPetDao  Query $com.focusflow.data.dao.VirtualPetDao  Update $com.focusflow.data.dao.VirtualPetDao  
VirtualPet $com.focusflow.data.dao.VirtualPetDao  kotlinx $com.focusflow.data.dao.VirtualPetDao  
AIInteraction com.focusflow.data.database  AIInteractionDao com.focusflow.data.database  Achievement com.focusflow.data.database  AchievementDao com.focusflow.data.database  BudgetCategory com.focusflow.data.database  BudgetCategoryDao com.focusflow.data.database  
Converters com.focusflow.data.database  
CreditCard com.focusflow.data.database  
CreditCardDao com.focusflow.data.database  Expense com.focusflow.data.database  
ExpenseDao com.focusflow.data.database  FocusFlowDatabase com.focusflow.data.database  HabitLog com.focusflow.data.database  HabitLogDao com.focusflow.data.database  String com.focusflow.data.database  Task com.focusflow.data.database  TaskDao com.focusflow.data.database  UserPreferences com.focusflow.data.database  UserPreferencesDao com.focusflow.data.database  	UserStats com.focusflow.data.database  UserStatsDao com.focusflow.data.database  
VirtualPet com.focusflow.data.database  
VirtualPetDao com.focusflow.data.database  Volatile com.focusflow.data.database  	LocalDate &com.focusflow.data.database.Converters  
LocalDateTime &com.focusflow.data.database.Converters  String &com.focusflow.data.database.Converters  
TypeConverter &com.focusflow.data.database.Converters  AIInteractionDao -com.focusflow.data.database.FocusFlowDatabase  AchievementDao -com.focusflow.data.database.FocusFlowDatabase  BudgetCategoryDao -com.focusflow.data.database.FocusFlowDatabase  Context -com.focusflow.data.database.FocusFlowDatabase  
CreditCardDao -com.focusflow.data.database.FocusFlowDatabase  
ExpenseDao -com.focusflow.data.database.FocusFlowDatabase  FocusFlowDatabase -com.focusflow.data.database.FocusFlowDatabase  HabitLogDao -com.focusflow.data.database.FocusFlowDatabase  TaskDao -com.focusflow.data.database.FocusFlowDatabase  UserPreferencesDao -com.focusflow.data.database.FocusFlowDatabase  UserStatsDao -com.focusflow.data.database.FocusFlowDatabase  
VirtualPetDao -com.focusflow.data.database.FocusFlowDatabase  Volatile -com.focusflow.data.database.FocusFlowDatabase  AIInteractionDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  AchievementDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  BudgetCategoryDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  Context 7com.focusflow.data.database.FocusFlowDatabase.Companion  
CreditCardDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  
ExpenseDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  FocusFlowDatabase 7com.focusflow.data.database.FocusFlowDatabase.Companion  HabitLogDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  TaskDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  UserPreferencesDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  UserStatsDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  
VirtualPetDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  Volatile 7com.focusflow.data.database.FocusFlowDatabase.Companion  
AIInteraction com.focusflow.data.model  AIInteractionDao com.focusflow.data.model  Achievement com.focusflow.data.model  AchievementDao com.focusflow.data.model  Boolean com.focusflow.data.model  BudgetCategory com.focusflow.data.model  BudgetCategoryDao com.focusflow.data.model  
Converters com.focusflow.data.model  
CreditCard com.focusflow.data.model  
CreditCardDao com.focusflow.data.model  Double com.focusflow.data.model  Expense com.focusflow.data.model  
ExpenseDao com.focusflow.data.model  HabitLog com.focusflow.data.model  HabitLogDao com.focusflow.data.model  Income com.focusflow.data.model  Int com.focusflow.data.model  List com.focusflow.data.model  Long com.focusflow.data.model  String com.focusflow.data.model  StringListConverter com.focusflow.data.model  Task com.focusflow.data.model  TaskDao com.focusflow.data.model  UserPreferences com.focusflow.data.model  UserPreferencesDao com.focusflow.data.model  	UserStats com.focusflow.data.model  UserStatsDao com.focusflow.data.model  
VirtualPet com.focusflow.data.model  
VirtualPetDao com.focusflow.data.model  Volatile com.focusflow.data.model  
LocalDateTime &com.focusflow.data.model.AIInteraction  Long &com.focusflow.data.model.AIInteraction  
PrimaryKey &com.focusflow.data.model.AIInteraction  String &com.focusflow.data.model.AIInteraction  Boolean $com.focusflow.data.model.Achievement  Int $com.focusflow.data.model.Achievement  
LocalDateTime $com.focusflow.data.model.Achievement  Long $com.focusflow.data.model.Achievement  
PrimaryKey $com.focusflow.data.model.Achievement  String $com.focusflow.data.model.Achievement  Boolean 'com.focusflow.data.model.BudgetCategory  Double 'com.focusflow.data.model.BudgetCategory  Int 'com.focusflow.data.model.BudgetCategory  Long 'com.focusflow.data.model.BudgetCategory  
PrimaryKey 'com.focusflow.data.model.BudgetCategory  String 'com.focusflow.data.model.BudgetCategory  Boolean #com.focusflow.data.model.CreditCard  Double #com.focusflow.data.model.CreditCard  	LocalDate #com.focusflow.data.model.CreditCard  Long #com.focusflow.data.model.CreditCard  
PrimaryKey #com.focusflow.data.model.CreditCard  String #com.focusflow.data.model.CreditCard  Boolean  com.focusflow.data.model.Expense  Double  com.focusflow.data.model.Expense  
LocalDateTime  com.focusflow.data.model.Expense  Long  com.focusflow.data.model.Expense  
PrimaryKey  com.focusflow.data.model.Expense  String  com.focusflow.data.model.Expense  	LocalDate !com.focusflow.data.model.HabitLog  Long !com.focusflow.data.model.HabitLog  
PrimaryKey !com.focusflow.data.model.HabitLog  String !com.focusflow.data.model.HabitLog  Boolean com.focusflow.data.model.Income  Double com.focusflow.data.model.Income  	LocalDate com.focusflow.data.model.Income  Long com.focusflow.data.model.Income  
PrimaryKey com.focusflow.data.model.Income  String com.focusflow.data.model.Income  List ,com.focusflow.data.model.StringListConverter  String ,com.focusflow.data.model.StringListConverter  
TypeConverter ,com.focusflow.data.model.StringListConverter  Boolean com.focusflow.data.model.Task  Int com.focusflow.data.model.Task  
LocalDateTime com.focusflow.data.model.Task  Long com.focusflow.data.model.Task  
PrimaryKey com.focusflow.data.model.Task  String com.focusflow.data.model.Task  Boolean (com.focusflow.data.model.UserPreferences  Double (com.focusflow.data.model.UserPreferences  Long (com.focusflow.data.model.UserPreferences  
PrimaryKey (com.focusflow.data.model.UserPreferences  String (com.focusflow.data.model.UserPreferences  Double "com.focusflow.data.model.UserStats  Int "com.focusflow.data.model.UserStats  
LocalDateTime "com.focusflow.data.model.UserStats  Long "com.focusflow.data.model.UserStats  
PrimaryKey "com.focusflow.data.model.UserStats  Int #com.focusflow.data.model.VirtualPet  List #com.focusflow.data.model.VirtualPet  
LocalDateTime #com.focusflow.data.model.VirtualPet  Long #com.focusflow.data.model.VirtualPet  
PrimaryKey #com.focusflow.data.model.VirtualPet  String #com.focusflow.data.model.VirtualPet  Boolean com.focusflow.data.repository  BudgetCategoryRepository com.focusflow.data.repository  CreditCardRepository com.focusflow.data.repository  Double com.focusflow.data.repository  ExpenseRepository com.focusflow.data.repository  Int com.focusflow.data.repository  List com.focusflow.data.repository  Long com.focusflow.data.repository  NotificationRepository com.focusflow.data.repository  Pair com.focusflow.data.repository  String com.focusflow.data.repository  UserPreferencesRepository com.focusflow.data.repository  BudgetCategory 6com.focusflow.data.repository.BudgetCategoryRepository  BudgetCategoryDao 6com.focusflow.data.repository.BudgetCategoryRepository  Double 6com.focusflow.data.repository.BudgetCategoryRepository  Flow 6com.focusflow.data.repository.BudgetCategoryRepository  Inject 6com.focusflow.data.repository.BudgetCategoryRepository  List 6com.focusflow.data.repository.BudgetCategoryRepository  Long 6com.focusflow.data.repository.BudgetCategoryRepository  String 6com.focusflow.data.repository.BudgetCategoryRepository  budgetCategoryDao 6com.focusflow.data.repository.BudgetCategoryRepository  
CreditCard 2com.focusflow.data.repository.CreditCardRepository  
CreditCardDao 2com.focusflow.data.repository.CreditCardRepository  Double 2com.focusflow.data.repository.CreditCardRepository  Flow 2com.focusflow.data.repository.CreditCardRepository  Inject 2com.focusflow.data.repository.CreditCardRepository  List 2com.focusflow.data.repository.CreditCardRepository  	LocalDate 2com.focusflow.data.repository.CreditCardRepository  Long 2com.focusflow.data.repository.CreditCardRepository  
creditCardDao 2com.focusflow.data.repository.CreditCardRepository  getAllActiveCreditCards 2com.focusflow.data.repository.CreditCardRepository  Double /com.focusflow.data.repository.ExpenseRepository  Expense /com.focusflow.data.repository.ExpenseRepository  
ExpenseDao /com.focusflow.data.repository.ExpenseRepository  Flow /com.focusflow.data.repository.ExpenseRepository  Inject /com.focusflow.data.repository.ExpenseRepository  List /com.focusflow.data.repository.ExpenseRepository  
LocalDateTime /com.focusflow.data.repository.ExpenseRepository  Long /com.focusflow.data.repository.ExpenseRepository  String /com.focusflow.data.repository.ExpenseRepository  
expenseDao /com.focusflow.data.repository.ExpenseRepository  getAllCategories /com.focusflow.data.repository.ExpenseRepository  getAllExpenses /com.focusflow.data.repository.ExpenseRepository  ApplicationContext 4com.focusflow.data.repository.NotificationRepository  Boolean 4com.focusflow.data.repository.NotificationRepository  Context 4com.focusflow.data.repository.NotificationRepository  Double 4com.focusflow.data.repository.NotificationRepository  FocusFlowNotificationManager 4com.focusflow.data.repository.NotificationRepository  Inject 4com.focusflow.data.repository.NotificationRepository  Int 4com.focusflow.data.repository.NotificationRepository  Long 4com.focusflow.data.repository.NotificationRepository  Pair 4com.focusflow.data.repository.NotificationRepository  String 4com.focusflow.data.repository.NotificationRepository  UserPreferencesRepository 4com.focusflow.data.repository.NotificationRepository  Boolean 7com.focusflow.data.repository.UserPreferencesRepository  Flow 7com.focusflow.data.repository.UserPreferencesRepository  Inject 7com.focusflow.data.repository.UserPreferencesRepository  String 7com.focusflow.data.repository.UserPreferencesRepository  UserPreferences 7com.focusflow.data.repository.UserPreferencesRepository  UserPreferencesDao 7com.focusflow.data.repository.UserPreferencesRepository  userPreferencesDao 7com.focusflow.data.repository.UserPreferencesRepository  AIInteractionDao com.focusflow.di  AchievementDao com.focusflow.di  BudgetCategoryDao com.focusflow.di  
CreditCardDao com.focusflow.di  DatabaseModule com.focusflow.di  
ExpenseDao com.focusflow.di  HabitLogDao com.focusflow.di  SingletonComponent com.focusflow.di  TaskDao com.focusflow.di  UserPreferencesDao com.focusflow.di  UserStatsDao com.focusflow.di  
VirtualPetDao com.focusflow.di  AIInteractionDao com.focusflow.di.DatabaseModule  AchievementDao com.focusflow.di.DatabaseModule  ApplicationContext com.focusflow.di.DatabaseModule  BudgetCategoryDao com.focusflow.di.DatabaseModule  Context com.focusflow.di.DatabaseModule  
CreditCardDao com.focusflow.di.DatabaseModule  
ExpenseDao com.focusflow.di.DatabaseModule  FocusFlowDatabase com.focusflow.di.DatabaseModule  HabitLogDao com.focusflow.di.DatabaseModule  Provides com.focusflow.di.DatabaseModule  	Singleton com.focusflow.di.DatabaseModule  TaskDao com.focusflow.di.DatabaseModule  UserPreferencesDao com.focusflow.di.DatabaseModule  UserStatsDao com.focusflow.di.DatabaseModule  
VirtualPetDao com.focusflow.di.DatabaseModule  CheckCircle com.focusflow.navigation  Favorite com.focusflow.navigation  Home com.focusflow.navigation  List com.focusflow.navigation  Person com.focusflow.navigation  Screen com.focusflow.navigation  Settings com.focusflow.navigation  ShoppingCart com.focusflow.navigation  Star com.focusflow.navigation  String com.focusflow.navigation  bottomNavItems com.focusflow.navigation  listOf com.focusflow.navigation  AICoach com.focusflow.navigation.Screen  CheckCircle com.focusflow.navigation.Screen  	Dashboard com.focusflow.navigation.Screen  Debt com.focusflow.navigation.Screen  Expenses com.focusflow.navigation.Screen  Favorite com.focusflow.navigation.Screen  Habits com.focusflow.navigation.Screen  Home com.focusflow.navigation.Screen  Icons com.focusflow.navigation.Screen  ImageVector com.focusflow.navigation.Screen  List com.focusflow.navigation.Screen  Person com.focusflow.navigation.Screen  Screen com.focusflow.navigation.Screen  Settings com.focusflow.navigation.Screen  ShoppingCart com.focusflow.navigation.Screen  Star com.focusflow.navigation.Screen  String com.focusflow.navigation.Screen  Tasks com.focusflow.navigation.Screen  Icons 'com.focusflow.navigation.Screen.AICoach  Person 'com.focusflow.navigation.Screen.AICoach  Icons &com.focusflow.navigation.Screen.Budget  List &com.focusflow.navigation.Screen.Budget  Home )com.focusflow.navigation.Screen.Dashboard  Icons )com.focusflow.navigation.Screen.Dashboard  Icons $com.focusflow.navigation.Screen.Debt  Star $com.focusflow.navigation.Screen.Debt  Icons (com.focusflow.navigation.Screen.Expenses  ShoppingCart (com.focusflow.navigation.Screen.Expenses  Favorite &com.focusflow.navigation.Screen.Habits  Icons &com.focusflow.navigation.Screen.Habits  Icons (com.focusflow.navigation.Screen.Settings  Settings (com.focusflow.navigation.Screen.Settings  CheckCircle %com.focusflow.navigation.Screen.Tasks  Icons %com.focusflow.navigation.Screen.Tasks  
AlarmReceiver com.focusflow.receiver  String com.focusflow.receiver  Context $com.focusflow.receiver.AlarmReceiver  Intent $com.focusflow.receiver.AlarmReceiver  NotificationCompat $com.focusflow.receiver.AlarmReceiver  String $com.focusflow.receiver.AlarmReceiver  Context com.focusflow.service  Double com.focusflow.service  FocusFlowNotificationManager com.focusflow.service  GamificationService com.focusflow.service  Int com.focusflow.service  List com.focusflow.service  Long com.focusflow.service  NotificationManagerCompat com.focusflow.service  NotificationService com.focusflow.service  String com.focusflow.service  AlarmManager 2com.focusflow.service.FocusFlowNotificationManager  ApplicationContext 2com.focusflow.service.FocusFlowNotificationManager  Context 2com.focusflow.service.FocusFlowNotificationManager  Inject 2com.focusflow.service.FocusFlowNotificationManager  Int 2com.focusflow.service.FocusFlowNotificationManager  List 2com.focusflow.service.FocusFlowNotificationManager  Long 2com.focusflow.service.FocusFlowNotificationManager  NotificationManagerCompat 2com.focusflow.service.FocusFlowNotificationManager  String 2com.focusflow.service.FocusFlowNotificationManager  context 2com.focusflow.service.FocusFlowNotificationManager  AlarmManager <com.focusflow.service.FocusFlowNotificationManager.Companion  ApplicationContext <com.focusflow.service.FocusFlowNotificationManager.Companion  Context <com.focusflow.service.FocusFlowNotificationManager.Companion  Inject <com.focusflow.service.FocusFlowNotificationManager.Companion  Int <com.focusflow.service.FocusFlowNotificationManager.Companion  List <com.focusflow.service.FocusFlowNotificationManager.Companion  Long <com.focusflow.service.FocusFlowNotificationManager.Companion  NotificationManagerCompat <com.focusflow.service.FocusFlowNotificationManager.Companion  String <com.focusflow.service.FocusFlowNotificationManager.Companion  AchievementDao )com.focusflow.service.GamificationService  Double )com.focusflow.service.GamificationService  Inject )com.focusflow.service.GamificationService  Int )com.focusflow.service.GamificationService  String )com.focusflow.service.GamificationService  UserStatsDao )com.focusflow.service.GamificationService  
VirtualPetDao )com.focusflow.service.GamificationService  Context )com.focusflow.service.NotificationService  IBinder )com.focusflow.service.NotificationService  Int )com.focusflow.service.NotificationService  Intent )com.focusflow.service.NotificationService  String )com.focusflow.service.NotificationService  Context 3com.focusflow.service.NotificationService.Companion  IBinder 3com.focusflow.service.NotificationService.Companion  Int 3com.focusflow.service.NotificationService.Companion  Intent 3com.focusflow.service.NotificationService.Companion  String 3com.focusflow.service.NotificationService.Companion  Boolean com.focusflow.ui.components  BottomNavigationBar com.focusflow.ui.components  BudgetPeriodDialog com.focusflow.ui.components  BudgetWarningCard com.focusflow.ui.components  ClearDataDialog com.focusflow.ui.components  ColumnScope com.focusflow.ui.components  
Composable com.focusflow.ui.components  CooldownPeriodCard com.focusflow.ui.components  Double com.focusflow.ui.components  ExportDataDialog com.focusflow.ui.components  FontSizeSelectionDialog com.focusflow.ui.components  GoalSelectionDialog com.focusflow.ui.components  ImpulseControlDialog com.focusflow.ui.components  ImpulseControlQuestions com.focusflow.ui.components  Int com.focusflow.ui.components  NotificationTimeDialog com.focusflow.ui.components  SpendingWatchlistCard com.focusflow.ui.components  String com.focusflow.ui.components  ThemeSelectionDialog com.focusflow.ui.components  Unit com.focusflow.ui.components  ADHDFriendlyStep com.focusflow.ui.onboarding  Boolean com.focusflow.ui.onboarding  BudgetSetupStep com.focusflow.ui.onboarding  CompleteStep com.focusflow.ui.onboarding  
Composable com.focusflow.ui.onboarding  
DebtSetupStep com.focusflow.ui.onboarding  FinancialGoalsStep com.focusflow.ui.onboarding  IncomeSetupStep com.focusflow.ui.onboarding  Int com.focusflow.ui.onboarding  List com.focusflow.ui.onboarding  NotificationSetupStep com.focusflow.ui.onboarding  OnboardingActivity com.focusflow.ui.onboarding  OnboardingProgressIndicator com.focusflow.ui.onboarding  OnboardingScreen com.focusflow.ui.onboarding  OnboardingStepLayout com.focusflow.ui.onboarding  PersonalGoalsStep com.focusflow.ui.onboarding  String com.focusflow.ui.onboarding  Unit com.focusflow.ui.onboarding  WelcomeStep com.focusflow.ui.onboarding  Bundle .com.focusflow.ui.onboarding.OnboardingActivity  
Composable .com.focusflow.ui.onboarding.OnboardingActivity  
AICoachScreen com.focusflow.ui.screens  AchievementBadge com.focusflow.ui.screens  AddBudgetCategoryDialog com.focusflow.ui.screens  AddCreditCardDialog com.focusflow.ui.screens  AddExpenseDialog com.focusflow.ui.screens  Boolean com.focusflow.ui.screens  BudgetAmountItem com.focusflow.ui.screens  BudgetCategoryItem com.focusflow.ui.screens  BudgetOverviewCard com.focusflow.ui.screens  BudgetScreen com.focusflow.ui.screens  ColumnScope com.focusflow.ui.screens  
Composable com.focusflow.ui.screens  CreditCardItem com.focusflow.ui.screens  CreditCardSummaryCard com.focusflow.ui.screens  DashboardScreen com.focusflow.ui.screens  DebtOverviewCard com.focusflow.ui.screens  
DebtScreen com.focusflow.ui.screens  Double com.focusflow.ui.screens  EmptyBudgetState com.focusflow.ui.screens  EmptyStateCard com.focusflow.ui.screens  EmptyStateMessage com.focusflow.ui.screens  ExpenseItem com.focusflow.ui.screens  ExpensesScreen com.focusflow.ui.screens  
FilterChip com.focusflow.ui.screens  HabitStreakCard com.focusflow.ui.screens  HabitStreakItem com.focusflow.ui.screens  HabitsScreen com.focusflow.ui.screens  Int com.focusflow.ui.screens  List com.focusflow.ui.screens  
MessageBubble com.focusflow.ui.screens  MessageInputField com.focusflow.ui.screens  MotivationalQuoteCard com.focusflow.ui.screens  Pair com.focusflow.ui.screens  
PaymentDialog com.focusflow.ui.screens  PayoffPlannerDialog com.focusflow.ui.screens  PayoffPlannerScreen com.focusflow.ui.screens  PayoffResultsSection com.focusflow.ui.screens  PayoffStepItem com.focusflow.ui.screens  PayoffSummaryCard com.focusflow.ui.screens  ProgressAchievementsCard com.focusflow.ui.screens  QuickCategoryOverview com.focusflow.ui.screens  QuickSetupDialog com.focusflow.ui.screens  SafeToSpendWidget com.focusflow.ui.screens  SettingsItem com.focusflow.ui.screens  SettingsScreen com.focusflow.ui.screens  SettingsSection com.focusflow.ui.screens  SettingsToggleItem com.focusflow.ui.screens  SpendingSummaryCard com.focusflow.ui.screens  StrategyComparisonCard com.focusflow.ui.screens  StrategySelectionCard com.focusflow.ui.screens  String com.focusflow.ui.screens  SuggestedPromptCard com.focusflow.ui.screens  SuggestedPromptsSection com.focusflow.ui.screens  SummaryItem com.focusflow.ui.screens  TaskItem com.focusflow.ui.screens  TasksScreen com.focusflow.ui.screens  TodaysTasksCard com.focusflow.ui.screens  TypingIndicator com.focusflow.ui.screens  Unit com.focusflow.ui.screens  VirtualPetWidget com.focusflow.ui.screens  com com.focusflow.ui.screens  
formatDate com.focusflow.ui.screens  Boolean com.focusflow.ui.theme  DarkColorPalette com.focusflow.ui.theme  FocusFlowTheme com.focusflow.ui.theme  LightColorPalette com.focusflow.ui.theme  	Purple200 com.focusflow.ui.theme  	Purple500 com.focusflow.ui.theme  	Purple700 com.focusflow.ui.theme  Shapes com.focusflow.ui.theme  Teal200 com.focusflow.ui.theme  
Typography com.focusflow.ui.theme  Unit com.focusflow.ui.theme  AICoachUiState com.focusflow.ui.viewmodel  AICoachViewModel com.focusflow.ui.viewmodel  Boolean com.focusflow.ui.viewmodel  
BudgetUiState com.focusflow.ui.viewmodel  BudgetViewModel com.focusflow.ui.viewmodel  ChatMessage com.focusflow.ui.viewmodel  DashboardUiState com.focusflow.ui.viewmodel  DashboardViewModel com.focusflow.ui.viewmodel  DebtUiState com.focusflow.ui.viewmodel  
DebtViewModel com.focusflow.ui.viewmodel  DefaultBudgetCategories com.focusflow.ui.viewmodel  Double com.focusflow.ui.viewmodel  ExpenseCategories com.focusflow.ui.viewmodel  ExpenseUiState com.focusflow.ui.viewmodel  ExpenseViewModel com.focusflow.ui.viewmodel  Flow com.focusflow.ui.viewmodel  Int com.focusflow.ui.viewmodel  List com.focusflow.ui.viewmodel  	LocalDate com.focusflow.ui.viewmodel  
LocalDateTime com.focusflow.ui.viewmodel  Long com.focusflow.ui.viewmodel  MainUiState com.focusflow.ui.viewmodel  
MainViewModel com.focusflow.ui.viewmodel  MutableStateFlow com.focusflow.ui.viewmodel  OnboardingStep com.focusflow.ui.viewmodel  OnboardingUiState com.focusflow.ui.viewmodel  OnboardingViewModel com.focusflow.ui.viewmodel  Pair com.focusflow.ui.viewmodel  PayoffCalculation com.focusflow.ui.viewmodel  
PayoffPlan com.focusflow.ui.viewmodel  
PayoffStep com.focusflow.ui.viewmodel  PayoffStrategy com.focusflow.ui.viewmodel  SettingsUiState com.focusflow.ui.viewmodel  SettingsViewModel com.focusflow.ui.viewmodel  	StateFlow com.focusflow.ui.viewmodel  String com.focusflow.ui.viewmodel  Triple com.focusflow.ui.viewmodel  asStateFlow com.focusflow.ui.viewmodel  com com.focusflow.ui.viewmodel  	emptyList com.focusflow.ui.viewmodel  listOf com.focusflow.ui.viewmodel  to com.focusflow.ui.viewmodel  Boolean )com.focusflow.ui.viewmodel.AICoachUiState  ChatMessage )com.focusflow.ui.viewmodel.AICoachUiState  List )com.focusflow.ui.viewmodel.AICoachUiState  String )com.focusflow.ui.viewmodel.AICoachUiState  AICoachUiState +com.focusflow.ui.viewmodel.AICoachViewModel  CreditCardRepository +com.focusflow.ui.viewmodel.AICoachViewModel  ExpenseRepository +com.focusflow.ui.viewmodel.AICoachViewModel  Inject +com.focusflow.ui.viewmodel.AICoachViewModel  MutableStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  	StateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  String +com.focusflow.ui.viewmodel.AICoachViewModel  UserPreferencesRepository +com.focusflow.ui.viewmodel.AICoachViewModel  _uiState +com.focusflow.ui.viewmodel.AICoachViewModel  asStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  getASStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  getAsStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  Boolean (com.focusflow.ui.viewmodel.BudgetUiState  BudgetCategory (com.focusflow.ui.viewmodel.BudgetUiState  Double (com.focusflow.ui.viewmodel.BudgetUiState  List (com.focusflow.ui.viewmodel.BudgetUiState  String (com.focusflow.ui.viewmodel.BudgetUiState  BudgetCategory *com.focusflow.ui.viewmodel.BudgetViewModel  BudgetCategoryRepository *com.focusflow.ui.viewmodel.BudgetViewModel  
BudgetUiState *com.focusflow.ui.viewmodel.BudgetViewModel  Double *com.focusflow.ui.viewmodel.BudgetViewModel  ExpenseRepository *com.focusflow.ui.viewmodel.BudgetViewModel  Inject *com.focusflow.ui.viewmodel.BudgetViewModel  Int *com.focusflow.ui.viewmodel.BudgetViewModel  
LocalDateTime *com.focusflow.ui.viewmodel.BudgetViewModel  MutableStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  	StateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  String *com.focusflow.ui.viewmodel.BudgetViewModel  Triple *com.focusflow.ui.viewmodel.BudgetViewModel  UserPreferencesRepository *com.focusflow.ui.viewmodel.BudgetViewModel  _uiState *com.focusflow.ui.viewmodel.BudgetViewModel  asStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  getASStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  getAsStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  Boolean &com.focusflow.ui.viewmodel.ChatMessage  Long &com.focusflow.ui.viewmodel.ChatMessage  String &com.focusflow.ui.viewmodel.ChatMessage  Boolean +com.focusflow.ui.viewmodel.DashboardUiState  Double +com.focusflow.ui.viewmodel.DashboardUiState  String +com.focusflow.ui.viewmodel.DashboardUiState  CreditCardRepository -com.focusflow.ui.viewmodel.DashboardViewModel  DashboardUiState -com.focusflow.ui.viewmodel.DashboardViewModel  ExpenseRepository -com.focusflow.ui.viewmodel.DashboardViewModel  Inject -com.focusflow.ui.viewmodel.DashboardViewModel  
LocalDateTime -com.focusflow.ui.viewmodel.DashboardViewModel  MutableStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  Pair -com.focusflow.ui.viewmodel.DashboardViewModel  	StateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  String -com.focusflow.ui.viewmodel.DashboardViewModel  UserPreferencesRepository -com.focusflow.ui.viewmodel.DashboardViewModel  _uiState -com.focusflow.ui.viewmodel.DashboardViewModel  asStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  getASStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  getAsStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  Boolean &com.focusflow.ui.viewmodel.DebtUiState  Double &com.focusflow.ui.viewmodel.DebtUiState  String &com.focusflow.ui.viewmodel.DebtUiState  
CreditCard (com.focusflow.ui.viewmodel.DebtViewModel  CreditCardRepository (com.focusflow.ui.viewmodel.DebtViewModel  DebtUiState (com.focusflow.ui.viewmodel.DebtViewModel  Double (com.focusflow.ui.viewmodel.DebtViewModel  Inject (com.focusflow.ui.viewmodel.DebtViewModel  List (com.focusflow.ui.viewmodel.DebtViewModel  	LocalDate (com.focusflow.ui.viewmodel.DebtViewModel  Long (com.focusflow.ui.viewmodel.DebtViewModel  MutableStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  PayoffCalculation (com.focusflow.ui.viewmodel.DebtViewModel  
PayoffPlan (com.focusflow.ui.viewmodel.DebtViewModel  PayoffStrategy (com.focusflow.ui.viewmodel.DebtViewModel  	StateFlow (com.focusflow.ui.viewmodel.DebtViewModel  String (com.focusflow.ui.viewmodel.DebtViewModel  _payoffPlan (com.focusflow.ui.viewmodel.DebtViewModel  _uiState (com.focusflow.ui.viewmodel.DebtViewModel  asStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  creditCardRepository (com.focusflow.ui.viewmodel.DebtViewModel  	emptyList (com.focusflow.ui.viewmodel.DebtViewModel  getASStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  getAsStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  getEMPTYList (com.focusflow.ui.viewmodel.DebtViewModel  getEmptyList (com.focusflow.ui.viewmodel.DebtViewModel  	getLISTOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  	getListOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  getTO 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  getTo 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  listOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  to 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  	getLISTOf ,com.focusflow.ui.viewmodel.ExpenseCategories  	getListOf ,com.focusflow.ui.viewmodel.ExpenseCategories  listOf ,com.focusflow.ui.viewmodel.ExpenseCategories  Boolean )com.focusflow.ui.viewmodel.ExpenseUiState  Double )com.focusflow.ui.viewmodel.ExpenseUiState  Expense )com.focusflow.ui.viewmodel.ExpenseUiState  List )com.focusflow.ui.viewmodel.ExpenseUiState  String )com.focusflow.ui.viewmodel.ExpenseUiState  Double +com.focusflow.ui.viewmodel.ExpenseViewModel  Expense +com.focusflow.ui.viewmodel.ExpenseViewModel  ExpenseRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  ExpenseUiState +com.focusflow.ui.viewmodel.ExpenseViewModel  Flow +com.focusflow.ui.viewmodel.ExpenseViewModel  Inject +com.focusflow.ui.viewmodel.ExpenseViewModel  List +com.focusflow.ui.viewmodel.ExpenseViewModel  
LocalDateTime +com.focusflow.ui.viewmodel.ExpenseViewModel  MutableStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  Pair +com.focusflow.ui.viewmodel.ExpenseViewModel  	StateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  String +com.focusflow.ui.viewmodel.ExpenseViewModel  UserPreferencesRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  _uiState +com.focusflow.ui.viewmodel.ExpenseViewModel  asStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  com +com.focusflow.ui.viewmodel.ExpenseViewModel  expenseRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  getASStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  getAsStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  Boolean &com.focusflow.ui.viewmodel.MainUiState  String &com.focusflow.ui.viewmodel.MainUiState  Inject (com.focusflow.ui.viewmodel.MainViewModel  MainUiState (com.focusflow.ui.viewmodel.MainViewModel  MutableStateFlow (com.focusflow.ui.viewmodel.MainViewModel  NotificationRepository (com.focusflow.ui.viewmodel.MainViewModel  	StateFlow (com.focusflow.ui.viewmodel.MainViewModel  UserPreferencesRepository (com.focusflow.ui.viewmodel.MainViewModel  _uiState (com.focusflow.ui.viewmodel.MainViewModel  asStateFlow (com.focusflow.ui.viewmodel.MainViewModel  com (com.focusflow.ui.viewmodel.MainViewModel  getASStateFlow (com.focusflow.ui.viewmodel.MainViewModel  getAsStateFlow (com.focusflow.ui.viewmodel.MainViewModel  Boolean ,com.focusflow.ui.viewmodel.OnboardingUiState  List ,com.focusflow.ui.viewmodel.OnboardingUiState  OnboardingStep ,com.focusflow.ui.viewmodel.OnboardingUiState  String ,com.focusflow.ui.viewmodel.OnboardingUiState  Boolean .com.focusflow.ui.viewmodel.OnboardingViewModel  Double .com.focusflow.ui.viewmodel.OnboardingViewModel  Inject .com.focusflow.ui.viewmodel.OnboardingViewModel  List .com.focusflow.ui.viewmodel.OnboardingViewModel  MutableStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  OnboardingUiState .com.focusflow.ui.viewmodel.OnboardingViewModel  	StateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  String .com.focusflow.ui.viewmodel.OnboardingViewModel  UserPreferencesRepository .com.focusflow.ui.viewmodel.OnboardingViewModel  _uiState .com.focusflow.ui.viewmodel.OnboardingViewModel  asStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  getASStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  getAsStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  Double ,com.focusflow.ui.viewmodel.PayoffCalculation  Int ,com.focusflow.ui.viewmodel.PayoffCalculation  Double %com.focusflow.ui.viewmodel.PayoffPlan  Int %com.focusflow.ui.viewmodel.PayoffPlan  List %com.focusflow.ui.viewmodel.PayoffPlan  
PayoffStep %com.focusflow.ui.viewmodel.PayoffPlan  Boolean %com.focusflow.ui.viewmodel.PayoffStep  Double %com.focusflow.ui.viewmodel.PayoffStep  Int %com.focusflow.ui.viewmodel.PayoffStep  String %com.focusflow.ui.viewmodel.PayoffStep  Boolean *com.focusflow.ui.viewmodel.SettingsUiState  String *com.focusflow.ui.viewmodel.SettingsUiState  UserPreferences *com.focusflow.ui.viewmodel.SettingsUiState  Boolean ,com.focusflow.ui.viewmodel.SettingsViewModel  Inject ,com.focusflow.ui.viewmodel.SettingsViewModel  MutableStateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  NotificationRepository ,com.focusflow.ui.viewmodel.SettingsViewModel  SettingsUiState ,com.focusflow.ui.viewmodel.SettingsViewModel  	StateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  String ,com.focusflow.ui.viewmodel.SettingsViewModel  UserPreferencesRepository ,com.focusflow.ui.viewmodel.SettingsViewModel  _uiState ,com.focusflow.ui.viewmodel.SettingsViewModel  asStateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  getASStateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  getAsStateFlow ,com.focusflow.ui.viewmodel.SettingsViewModel  AccessibilityUtils com.focusflow.utils  Boolean com.focusflow.utils  DataIntegrityUtils com.focusflow.utils  Double com.focusflow.utils  ErrorHandler com.focusflow.utils  
ErrorSnackbar com.focusflow.utils  List com.focusflow.utils  Long com.focusflow.utils  PerformanceMonitor com.focusflow.utils  
SecurityUtils com.focusflow.utils  String com.focusflow.utils  	Throwable com.focusflow.utils  Unit com.focusflow.utils  ValidationUtils com.focusflow.utils  androidx com.focusflow.utils  com com.focusflow.utils  mutableMapOf com.focusflow.utils  Double &com.focusflow.utils.AccessibilityUtils  String &com.focusflow.utils.AccessibilityUtils  androidx &com.focusflow.utils.AccessibilityUtils  List &com.focusflow.utils.DataIntegrityUtils  String &com.focusflow.utils.DataIntegrityUtils  com &com.focusflow.utils.DataIntegrityUtils  SnackbarDuration  com.focusflow.utils.ErrorHandler  SnackbarHostState  com.focusflow.utils.ErrorHandler  String  com.focusflow.utils.ErrorHandler  	Throwable  com.focusflow.utils.ErrorHandler  Long &com.focusflow.utils.PerformanceMonitor  String &com.focusflow.utils.PerformanceMonitor  getMUTABLEMapOf &com.focusflow.utils.PerformanceMonitor  getMutableMapOf &com.focusflow.utils.PerformanceMonitor  mutableMapOf &com.focusflow.utils.PerformanceMonitor  Boolean !com.focusflow.utils.SecurityUtils  String !com.focusflow.utils.SecurityUtils  Boolean #com.focusflow.utils.ValidationUtils  Double #com.focusflow.utils.ValidationUtils  String #com.focusflow.utils.ValidationUtils  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  AICoachUiState 	java.lang  
AIInteraction 	java.lang  Achievement 	java.lang  BudgetCategory 	java.lang  
BudgetUiState 	java.lang  Context 	java.lang  
Converters 	java.lang  
CreditCard 	java.lang  DashboardUiState 	java.lang  DebtUiState 	java.lang  Expense 	java.lang  ExpenseUiState 	java.lang  HabitLog 	java.lang  MainUiState 	java.lang  MutableStateFlow 	java.lang  NotificationManagerCompat 	java.lang  OnConflictStrategy 	java.lang  OnboardingUiState 	java.lang  
PayoffPlan 	java.lang  Screen 	java.lang  SettingsUiState 	java.lang  SingletonComponent 	java.lang  StringListConverter 	java.lang  Task 	java.lang  UserPreferences 	java.lang  	UserStats 	java.lang  
VirtualPet 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  com 	java.lang  	emptyList 	java.lang  kotlinx 	java.lang  listOf 	java.lang  mutableMapOf 	java.lang  to 	java.lang  SimpleDateFormat 	java.text  
Composable 	java.util  Context 	java.util  NotificationManagerCompat 	java.util  Inject javax.inject  	Singleton javax.inject  AICoachUiState kotlin  
AIInteraction kotlin  Achievement kotlin  Any kotlin  Array kotlin  Boolean kotlin  BudgetCategory kotlin  
BudgetUiState kotlin  Context kotlin  
Converters kotlin  
CreditCard kotlin  DashboardUiState kotlin  DebtUiState kotlin  Double kotlin  Expense kotlin  ExpenseUiState kotlin  HabitLog kotlin  Int kotlin  Long kotlin  MainUiState kotlin  MutableStateFlow kotlin  Nothing kotlin  NotificationManagerCompat kotlin  OnConflictStrategy kotlin  OnboardingUiState kotlin  Pair kotlin  
PayoffPlan kotlin  Screen kotlin  SettingsUiState kotlin  SingletonComponent kotlin  String kotlin  StringListConverter kotlin  Task kotlin  	Throwable kotlin  Triple kotlin  Unit kotlin  UserPreferences kotlin  	UserStats kotlin  
VirtualPet kotlin  Volatile kotlin  androidx kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  	emptyList kotlin  kotlinx kotlin  listOf kotlin  mutableMapOf kotlin  to kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getTO 
kotlin.String  getTo 
kotlin.String  AICoachUiState kotlin.annotation  
AIInteraction kotlin.annotation  Achievement kotlin.annotation  BudgetCategory kotlin.annotation  
BudgetUiState kotlin.annotation  Context kotlin.annotation  
Converters kotlin.annotation  
CreditCard kotlin.annotation  DashboardUiState kotlin.annotation  DebtUiState kotlin.annotation  Expense kotlin.annotation  ExpenseUiState kotlin.annotation  HabitLog kotlin.annotation  MainUiState kotlin.annotation  MutableStateFlow kotlin.annotation  NotificationManagerCompat kotlin.annotation  OnConflictStrategy kotlin.annotation  OnboardingUiState kotlin.annotation  Pair kotlin.annotation  
PayoffPlan kotlin.annotation  Screen kotlin.annotation  SettingsUiState kotlin.annotation  SingletonComponent kotlin.annotation  StringListConverter kotlin.annotation  Task kotlin.annotation  Triple kotlin.annotation  UserPreferences kotlin.annotation  	UserStats kotlin.annotation  
VirtualPet kotlin.annotation  Volatile kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  kotlinx kotlin.annotation  listOf kotlin.annotation  mutableMapOf kotlin.annotation  to kotlin.annotation  AICoachUiState kotlin.collections  
AIInteraction kotlin.collections  Achievement kotlin.collections  BudgetCategory kotlin.collections  
BudgetUiState kotlin.collections  Context kotlin.collections  
Converters kotlin.collections  
CreditCard kotlin.collections  DashboardUiState kotlin.collections  DebtUiState kotlin.collections  Expense kotlin.collections  ExpenseUiState kotlin.collections  HabitLog kotlin.collections  List kotlin.collections  MainUiState kotlin.collections  
MutableMap kotlin.collections  MutableStateFlow kotlin.collections  NotificationManagerCompat kotlin.collections  OnConflictStrategy kotlin.collections  OnboardingUiState kotlin.collections  Pair kotlin.collections  
PayoffPlan kotlin.collections  Screen kotlin.collections  SettingsUiState kotlin.collections  SingletonComponent kotlin.collections  StringListConverter kotlin.collections  Task kotlin.collections  Triple kotlin.collections  UserPreferences kotlin.collections  	UserStats kotlin.collections  
VirtualPet kotlin.collections  Volatile kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  kotlinx kotlin.collections  listOf kotlin.collections  mutableMapOf kotlin.collections  to kotlin.collections  AICoachUiState kotlin.comparisons  
AIInteraction kotlin.comparisons  Achievement kotlin.comparisons  BudgetCategory kotlin.comparisons  
BudgetUiState kotlin.comparisons  Context kotlin.comparisons  
Converters kotlin.comparisons  
CreditCard kotlin.comparisons  DashboardUiState kotlin.comparisons  DebtUiState kotlin.comparisons  Expense kotlin.comparisons  ExpenseUiState kotlin.comparisons  HabitLog kotlin.comparisons  MainUiState kotlin.comparisons  MutableStateFlow kotlin.comparisons  NotificationManagerCompat kotlin.comparisons  OnConflictStrategy kotlin.comparisons  OnboardingUiState kotlin.comparisons  Pair kotlin.comparisons  
PayoffPlan kotlin.comparisons  Screen kotlin.comparisons  SettingsUiState kotlin.comparisons  SingletonComponent kotlin.comparisons  StringListConverter kotlin.comparisons  Task kotlin.comparisons  Triple kotlin.comparisons  UserPreferences kotlin.comparisons  	UserStats kotlin.comparisons  
VirtualPet kotlin.comparisons  Volatile kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  kotlinx kotlin.comparisons  listOf kotlin.comparisons  mutableMapOf kotlin.comparisons  to kotlin.comparisons  AICoachUiState 	kotlin.io  
AIInteraction 	kotlin.io  Achievement 	kotlin.io  BudgetCategory 	kotlin.io  
BudgetUiState 	kotlin.io  Context 	kotlin.io  
Converters 	kotlin.io  
CreditCard 	kotlin.io  DashboardUiState 	kotlin.io  DebtUiState 	kotlin.io  Expense 	kotlin.io  ExpenseUiState 	kotlin.io  HabitLog 	kotlin.io  MainUiState 	kotlin.io  MutableStateFlow 	kotlin.io  NotificationManagerCompat 	kotlin.io  OnConflictStrategy 	kotlin.io  OnboardingUiState 	kotlin.io  Pair 	kotlin.io  
PayoffPlan 	kotlin.io  Screen 	kotlin.io  SettingsUiState 	kotlin.io  SingletonComponent 	kotlin.io  StringListConverter 	kotlin.io  Task 	kotlin.io  Triple 	kotlin.io  UserPreferences 	kotlin.io  	UserStats 	kotlin.io  
VirtualPet 	kotlin.io  Volatile 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  kotlinx 	kotlin.io  listOf 	kotlin.io  mutableMapOf 	kotlin.io  to 	kotlin.io  AICoachUiState 
kotlin.jvm  
AIInteraction 
kotlin.jvm  Achievement 
kotlin.jvm  BudgetCategory 
kotlin.jvm  
BudgetUiState 
kotlin.jvm  Context 
kotlin.jvm  
Converters 
kotlin.jvm  
CreditCard 
kotlin.jvm  DashboardUiState 
kotlin.jvm  DebtUiState 
kotlin.jvm  Expense 
kotlin.jvm  ExpenseUiState 
kotlin.jvm  HabitLog 
kotlin.jvm  MainUiState 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  NotificationManagerCompat 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  OnboardingUiState 
kotlin.jvm  Pair 
kotlin.jvm  
PayoffPlan 
kotlin.jvm  Screen 
kotlin.jvm  SettingsUiState 
kotlin.jvm  SingletonComponent 
kotlin.jvm  StringListConverter 
kotlin.jvm  Task 
kotlin.jvm  Triple 
kotlin.jvm  UserPreferences 
kotlin.jvm  	UserStats 
kotlin.jvm  
VirtualPet 
kotlin.jvm  Volatile 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  kotlinx 
kotlin.jvm  listOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  to 
kotlin.jvm  AICoachUiState 
kotlin.ranges  
AIInteraction 
kotlin.ranges  Achievement 
kotlin.ranges  BudgetCategory 
kotlin.ranges  
BudgetUiState 
kotlin.ranges  Context 
kotlin.ranges  
Converters 
kotlin.ranges  
CreditCard 
kotlin.ranges  DashboardUiState 
kotlin.ranges  DebtUiState 
kotlin.ranges  Expense 
kotlin.ranges  ExpenseUiState 
kotlin.ranges  HabitLog 
kotlin.ranges  MainUiState 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  NotificationManagerCompat 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  OnboardingUiState 
kotlin.ranges  Pair 
kotlin.ranges  
PayoffPlan 
kotlin.ranges  Screen 
kotlin.ranges  SettingsUiState 
kotlin.ranges  SingletonComponent 
kotlin.ranges  StringListConverter 
kotlin.ranges  Task 
kotlin.ranges  Triple 
kotlin.ranges  UserPreferences 
kotlin.ranges  	UserStats 
kotlin.ranges  
VirtualPet 
kotlin.ranges  Volatile 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  kotlinx 
kotlin.ranges  listOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  to 
kotlin.ranges  KClass kotlin.reflect  AICoachUiState kotlin.sequences  
AIInteraction kotlin.sequences  Achievement kotlin.sequences  BudgetCategory kotlin.sequences  
BudgetUiState kotlin.sequences  Context kotlin.sequences  
Converters kotlin.sequences  
CreditCard kotlin.sequences  DashboardUiState kotlin.sequences  DebtUiState kotlin.sequences  Expense kotlin.sequences  ExpenseUiState kotlin.sequences  HabitLog kotlin.sequences  MainUiState kotlin.sequences  MutableStateFlow kotlin.sequences  NotificationManagerCompat kotlin.sequences  OnConflictStrategy kotlin.sequences  OnboardingUiState kotlin.sequences  Pair kotlin.sequences  
PayoffPlan kotlin.sequences  Screen kotlin.sequences  SettingsUiState kotlin.sequences  SingletonComponent kotlin.sequences  StringListConverter kotlin.sequences  Task kotlin.sequences  Triple kotlin.sequences  UserPreferences kotlin.sequences  	UserStats kotlin.sequences  
VirtualPet kotlin.sequences  Volatile kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  kotlinx kotlin.sequences  listOf kotlin.sequences  mutableMapOf kotlin.sequences  to kotlin.sequences  AICoachUiState kotlin.text  
AIInteraction kotlin.text  Achievement kotlin.text  BudgetCategory kotlin.text  
BudgetUiState kotlin.text  Context kotlin.text  
Converters kotlin.text  
CreditCard kotlin.text  DashboardUiState kotlin.text  DebtUiState kotlin.text  Expense kotlin.text  ExpenseUiState kotlin.text  HabitLog kotlin.text  MainUiState kotlin.text  MutableStateFlow kotlin.text  NotificationManagerCompat kotlin.text  OnConflictStrategy kotlin.text  OnboardingUiState kotlin.text  Pair kotlin.text  
PayoffPlan kotlin.text  Screen kotlin.text  SettingsUiState kotlin.text  SingletonComponent kotlin.text  StringListConverter kotlin.text  Task kotlin.text  Triple kotlin.text  UserPreferences kotlin.text  	UserStats kotlin.text  
VirtualPet kotlin.text  Volatile kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  com kotlin.text  	emptyList kotlin.text  kotlinx kotlin.text  listOf kotlin.text  mutableMapOf kotlin.text  to kotlin.text  CoroutineScope kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  AICoachUiState kotlinx.coroutines.flow  
BudgetUiState kotlinx.coroutines.flow  DashboardUiState kotlinx.coroutines.flow  DebtUiState kotlinx.coroutines.flow  ExpenseUiState kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  	LocalDate kotlinx.coroutines.flow  
LocalDateTime kotlinx.coroutines.flow  MainUiState kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  OnboardingUiState kotlinx.coroutines.flow  Pair kotlinx.coroutines.flow  
PayoffPlan kotlinx.coroutines.flow  SettingsUiState kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  Triple kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  com kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  first kotlinx.coroutines.flow  listOf kotlinx.coroutines.flow  to kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  
BudgetUiState kotlinx.datetime  Clock kotlinx.datetime  DashboardUiState kotlinx.datetime  DebtUiState kotlinx.datetime  ExpenseUiState kotlinx.datetime  Flow kotlinx.datetime  	LocalDate kotlinx.datetime  
LocalDateTime kotlinx.datetime  MutableStateFlow kotlinx.datetime  Pair kotlinx.datetime  
PayoffPlan kotlinx.datetime  	StateFlow kotlinx.datetime  TimeZone kotlinx.datetime  Triple kotlinx.datetime  asStateFlow kotlinx.datetime  com kotlinx.datetime  	emptyList kotlinx.datetime  listOf kotlinx.datetime  to kotlinx.datetime  toLocalDateTime kotlinx.datetime                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         