<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android" android:width="24dp"
        android:height="24dp" android:viewportHeight="24" android:viewportWidth="24">
    <group android:name="_R_G">
        <group android:name="_R_G_L_3_G" android:pivotX="-33" android:pivotY="-34"
               android:rotation="0" android:scaleX="0.738" android:scaleY="0.738"
               android:translateX="45" android:translateY="46.4">
            <path android:name="_R_G_L_3_G_D_0_P_0" android:pathData=" M-25.36 -24.41 C-25.93,-24.31 -26.49,-24.27 -26.81,-24.27 C-28.11,-24.27 -29.35,-24.62 -30.43,-25.4 C-32.11,-26.6 -33.2,-28.57 -33.2,-30.79 "
                  android:strokeAlpha="1" android:strokeColor="#ff008577"
                  android:strokeLineCap="round" android:strokeLineJoin="round"
                  android:strokeWidth="1.45" android:trimPathEnd="1"
                  android:trimPathOffset="0"
                  android:trimPathStart="0"/>
            <path android:name="_R_G_L_3_G_D_1_P_0" android:pathData=" M-36.14 -21.78 C-37.15,-22.98 -37.72,-23.7 -38.51,-25.29 C-39.33,-26.94 -39.82,-28.78 -39.82,-30.77 C-39.82,-34.43 -36.85,-37.4 -33.19,-37.4 C-29.52,-37.4 -26.55,-34.43 -26.55,-30.77 "
                  android:strokeAlpha="1" android:strokeColor="#ff008577"
                  android:strokeLineCap="round" android:strokeLineJoin="round"
                  android:strokeWidth="1.45" android:trimPathEnd="1"
                  android:trimPathOffset="0"
                  android:trimPathStart="0"/>
            <path android:name="_R_G_L_3_G_D_2_P_0" android:pathData=" M-42.19 -25.68 C-42.95,-27.82 -43.09,-29.54 -43.09,-30.8 C-43.09,-32.27 -42.84,-33.65 -42.27,-34.9 C-40.71,-38.35 -37.24,-40.75 -33.2,-40.75 C-27.71,-40.75 -23.26,-36.3 -23.26,-30.8 C-23.26,-28.97 -24.74,-27.49 -26.57,-27.49 C-28.4,-27.49 -29.89,-28.97 -29.89,-30.8 C-29.89,-32.64 -31.37,-34.12 -33.2,-34.12 C-35.04,-34.12 -36.52,-32.64 -36.52,-30.8 C-36.52,-28.23 -35.53,-25.92 -33.92,-24.22 C-32.69,-22.93 -31.48,-22.12 -29.44,-21.53 "
                  android:strokeAlpha="1" android:strokeColor="#ff008577"
                  android:strokeLineCap="round" android:strokeLineJoin="round"
                  android:strokeWidth="1.45" android:trimPathEnd="1"
                  android:trimPathOffset="0"
                  android:trimPathStart="0"/>
            <path android:name="_R_G_L_3_G_D_3_P_0" android:pathData=" M-44.06 -38.17 C-42.87,-39.94 -41.39,-41.41 -39.51,-42.44 C-37.62,-43.47 -35.46,-44.05 -33.16,-44.05 C-30.88,-44.05 -28.72,-43.47 -26.85,-42.45 C-24.97,-41.43 -23.48,-39.97 -22.29,-38.21 "
                  android:strokeAlpha="1" android:strokeColor="#ff008577"
                  android:strokeLineCap="round" android:strokeLineJoin="round"
                  android:strokeWidth="1.45" android:trimPathEnd="1"
                  android:trimPathOffset="0"
                  android:trimPathStart="0"/>
            <path android:name="_R_G_L_3_G_D_4_P_0" android:pathData=" M-25.72 -45.45 C-27.99,-46.76 -30.43,-47.52 -33.28,-47.52 C-36.13,-47.52 -38.51,-46.74 -40.62,-45.45 "
                  android:strokeAlpha="1" android:strokeColor="#ff008577"
                  android:strokeLineCap="round" android:strokeLineJoin="round"
                  android:strokeWidth="1.45" android:trimPathEnd="1"
                  android:trimPathOffset="0"
                  android:trimPathStart="0"/>
        </group>
    </group>
</vector>